import tkinter as tk
from tkinter import ttk
import apiManager as api
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np
from plotManager import PlotManager

# Globale Variable um die Standardwerte für das Start- und Enddatum zu setzen
STANDARD_START_DATE = "2025-01-01"
STANDARD_END_DATE = "2025-02-01"

#----------------------------------------------------------------------------------------------
# Funktionsbeschreibung
# Überprüft die Eingabe des KryptoSymbols (BTCEUR)
# auf Falscheingaben (Sonderzeichen, Leerz<PERSON>chen, Zahlen am Anfang, etc.)
#----------------------------------------------------------------------------------------------
def validate_symbol(symbol):
    # Binance-Symbole bestehen typischerweise aus Buchstaben und können Zahlen enthalten
    if not symbol or not isinstance(symbol, str):
        return False

    # Entfernen der Leerzeichen und prüfe, ob das Symbol nicht leer ist
    symbol = symbol.strip()
    if not symbol:
        return False

    # Prüfen, ob das Symbol nur alphanumerische Zeichen enthält(keine Sonderzeichen)
    if not symbol.isalnum():
        return False

    return True

#----------------------------------------------------------------------------------------------
# Funktionsbeschreibung
# Zum Laden von den abgerufenen Daten
# Bei Auswahl "Erweitert" wird die Erweiterte Version geöffnet
#----------------------------------------------------------------------------------------------
def OpenKrypto(Coin_TAG):
    # Kontrolliert Namen bei der Internetseite (Api)
    if validate_symbol(Coin_TAG):
        print("Symbol ist gültig")
    else:
        print("Symbol ist ungültig")
        return None

    print("Open Krypto")
    data = apimanager.fetch_data(Coin_TAG, interval_var.get(), entry_startDate.get(), entry_endDate.get()) # Lädt die Daten von der API

    if data:
        print(f"Daten erfolgreich geladen! Anzahl Datenpunkte: {len(data)}")
        # Erstellt ein neues Fenster mit den geladenen Daten
        if chart_type_var.get() == "Erweitert":
            plot_manager = PlotManager() # Erstellt eine Instanz der PlotManager-Klasse
            plot_manager.create_advanced_chart_window(data, Coin_TAG, interval_var.get())
        else:
            openKryptoWindow(data, Coin_TAG, interval_var.get())# Erstellt ein neues Fenster mit den geladenen Daten

    else:
        print("Fehler beim Laden der Daten!")

#----------------------------------------------------------------------------------------------
# Funktionsbeschreibung
# Neues Fenster für die Grafik erstellen
# Graphen Beschriftung erstellen
# Daten in den Graphen plotten
#----------------------------------------------------------------------------------------------
def openKryptoWindow(data, symbol, interval):

    # Erstellt ein neues Fenster
    kryptoWindow = tk.Toplevel(root)
    kryptoWindow.geometry("1920x1080")
    kryptoWindow.title(f"Krypto Chart - {symbol} ({interval})")

    # Matplotlib Figure erstellen
    fig = Figure(figsize=(12, 8), dpi=100)
    ax = fig.add_subplot(111)

    # Daten für das Diagramm vorbereiten
    prices = [ohlvc.close for ohlvc in data]
    volumes = [ohlvc.volume for ohlvc in data]

    # Timestamps zu datetime-Objekten konvertieren
    timestamps = []
    for ohlvc in data:
        # Binance Timestamps sind in Millisekunden, datetime.fromtimestamp erwartet Sekunden
        dt = datetime.fromtimestamp(ohlvc.timestamp / 1000)
        timestamps.append(dt)

    # Hauptdiagramm: Preisverlauf
    ax.plot(timestamps, prices, linewidth=2, color='#1f77b4', label=f'{symbol} Preis')

    # Durchschnittspreis berechnen und als horizontale Linie anzeigen
    avg_price = sum(prices) / len(prices)
    ax.axhline(y=avg_price, color='grey', linestyle='--', linewidth=2, alpha=0.8, label=f'Durchschnitt: {avg_price:.2f}')

    # Min- und Max-Preise markieren
    min_price = min(prices)
    max_price = max(prices)
    min_index = prices.index(min_price)
    max_index = prices.index(max_price)

    # Min-Preis markieren (roter Punkt)
    ax.plot(timestamps[min_index], min_price, 'ro', markersize=8, label=f'Min: {min_price:.2f}')
    

    # Max-Preis markieren (grüner Punkt)
    ax.plot(timestamps[max_index], max_price, 'go', markersize=8, label=f'Max: {max_price:.2f}')
    

    ax.set_title(f'{symbol} Preisverlauf ({interval})', fontsize=16, fontweight='bold')
    ax.set_xlabel('Zeit', fontsize=12)
    ax.set_ylabel('Preis (USDT)', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.legend()

    # X-Achse formatieren für bessere Lesbarkeit
    # Intelligente Formatierung basierend auf Datenmenge
    data_count = len(timestamps)

    if data_count > 100:  # Viele Datenpunkte
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=12))
    elif data_count > 50:  # Mittlere Anzahl
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=12))
    else:  # Wenige Datenpunkte
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))

    # X-Achsen-Labels optimieren
    plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=9)

    # Mehr Platz für X-Achsen-Labels
    fig.subplots_adjust(bottom=0.15)

    # Sekundäre Y-Achse für Volumen
    ax2 = ax.twinx()
    ax2.bar(timestamps, volumes, alpha=0.3, color='orange', label='Volumen', width=0.02)
    ax2.set_ylabel('Volumen', fontsize=12, color='orange')
    ax2.tick_params(axis='y', labelcolor='orange')

    # Layout optimieren
    fig.tight_layout()

    # Canvas für Matplotlib in Tkinter 
    canvas = FigureCanvasTkAgg(fig, master=kryptoWindow)
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)

    # Navigationsleiste hinzufügen
    toolbar = NavigationToolbar2Tk(canvas, kryptoWindow)
    toolbar.update()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)

    # Informations-Frame am unteren Rand
    info_frame = tk.Frame(kryptoWindow, bg='lightgray', height=80)
    info_frame.pack(side=tk.BOTTOM, fill=tk.X)
    info_frame.pack_propagate(False)

    # Statistiken berechnen und anzeigen
    if data:
        current_price = data[-1].close
        min_price = min(ohlvc.close for ohlvc in data)
        max_price = max(ohlvc.close for ohlvc in data)
        avg_price = sum(ohlvc.close for ohlvc in data) / len(data)
        total_volume = sum(ohlvc.volume for ohlvc in data)

        # Finde die tatsächlichen Datumswerte für Min- und Max-Preise
        min_price_ohlvc = min(data, key=lambda x: x.close)
        max_price_ohlvc = max(data, key=lambda x: x.close)
        min_price_datum = datetime.fromtimestamp(min_price_ohlvc.timestamp / 1000).strftime('%m-%d %H:%M')
        max_price_datum = datetime.fromtimestamp(max_price_ohlvc.timestamp / 1000).strftime('%m-%d %H:%M')

        # Statistik-Labels
        stats_text = f"Letzter Preis: {current_price:.2f} | Min: {min_price:.2f} am {min_price_datum} | Max: {max_price:.2f} am {max_price_datum} | Durchschnitt: {avg_price:.2f}"
        volume_text = f"Gesamtvolumen: {total_volume:.2f} | Datenpunkte: {len(data)}"

        tk.Label(info_frame, text=stats_text, bg='lightgray', font=('Arial', 10, 'bold')).pack(pady=5)
        tk.Label(info_frame, text=volume_text, bg='lightgray', font=('Arial', 9)).pack()

    # Schließen-Button
    close_btn = tk.Button(kryptoWindow, text="Fenster schließen",
                         command=kryptoWindow.destroy,
                         bg='red', fg='white', font=('Arial', 10, 'bold'))
    close_btn.config(width=15, height=2)
    close_btn.pack(side=tk.BOTTOM, padx=10, pady=10)

#----------------------------------------------------------------------------------------------
# Funktionsbeschreibung
# Prüft die Eingabefelder und sperrt den "Open Krypto" Button falls nicht alle Felder ausgefüllt sind
# Wird bei jeder Änderung in den Eingabefeldern aufgerufen
#----------------------------------------------------------------------------------------------
def checkEingabefelder(*args):
    if entryKrypto.get() and entry_startDate.get() and entry_endDate.get():
        btn_OpenKrypto.config(state= tk.NORMAL)
    else:
        btn_OpenKrypto.config(state= tk.DISABLED)

#----------------------------------------------------------------------------------------------
# Funktionsbeschreibung
# GUI Erstellung
# Packt alle Controls in das GUI
#---------------------------------------------------------------------------------------------- 

if __name__ == '__main__':
    root = tk.Tk()
    root.geometry("250x700")

    apimanager = api.APIManager()  # Alle Funktionen der Klasse können hiermit verwendet werden
    
    label_welcome = tk.Label(root, text="Welcome to CryptoGUI")
    label_welcome.pack(pady=10)

    btn_OpenKrypto = tk.Button(root, text="Open Krypto",command= lambda: OpenKrypto(entryKrypto.get()))
    btn_OpenKrypto.pack(pady=10,side = tk.BOTTOM)
    btn_OpenKrypto.config(state= tk.DISABLED)
    
    # Intervall als Combobox (Dropdown)
    label_Intervall = tk.Label(root, text="Intervall")
    label_Intervall.pack(pady=10)

    interval_var = tk.StringVar(root)
    interval_var.set("1h")
    comboBox_Intervall = tk.OptionMenu(root,interval_var, "1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1M")
    comboBox_Intervall.config(width=10)
    comboBox_Intervall.pack(pady=10)

    # Chart-Typ / Standard und Erweitert
    label_ChartType = tk.Label(root, text="Chart-Typ")
    label_ChartType.pack(pady=10)

    chart_type_var = tk.StringVar(root)
    chart_type_var.set("Standard")
    comboBox_ChartType = tk.OptionMenu(root, chart_type_var, "Standard", "Erweitert")
    comboBox_ChartType.config(width=10)
    comboBox_ChartType.pack(pady=10)
    
    Krypto_Label = tk.Label(root, text="Krypto Coin Symbol")
    Krypto_Label.pack(pady=10)
    entryKrypto = tk.Entry(root)
    entryKrypto.pack(pady=10)
    
    label_startDate = tk.Label(root, text="Start Date")
    label_startDate.pack(pady=10)
    entry_startDate = tk.Entry(root)
    entry_startDate.pack(pady=10)
    
    label_endDate = tk.Label(root, text="End Date")
    label_endDate.pack(pady=10)
    entry_endDate = tk.Entry(root)
    entry_endDate.pack(pady=10)
   
    # Überprüfung ob alle Eingabefelder gefüllt sind 
    entryKrypto.bind("<KeyRelease>",checkEingabefelder)
    entry_startDate.bind("<KeyRelease>",checkEingabefelder)
    entry_endDate.bind("<KeyRelease>",checkEingabefelder)

    # Setze die Standardwerte für Start- und Enddatum
    entry_startDate.insert(0, STANDARD_START_DATE)
    entry_endDate.insert(0, STANDARD_END_DATE)   
    




    root.mainloop()
