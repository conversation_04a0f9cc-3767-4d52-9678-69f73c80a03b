import tkinter as tk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np

class PlotManager:
    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Initialisiert den PlotManager mit leeren Standardwerten
    # Speichert aktuelle Daten, Symbol und Intervall für Chart-Erstellung
    #----------------------------------------------------------------------------------------------
    def __init__(self):
        self.current_data = None
        self.current_symbol = None
        self.current_interval = None

    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Liniendiagramm mit Preis, Volumen und Markierung von Min-/Max-Preisen
    # Formatiert das Intervall der Datumsanzeige im Graphen je nach abgerufenen Datenpunkten
    #----------------------------------------------------------------------------------------------
    def create_line_chart(self, data, symbol, interval):

        # Matplotlib Figure erstellen
        fig = Figure(figsize=(12, 8), dpi=100)
        ax = fig.add_subplot(111)

        # Daten vorbereiten
        prices = [ohlvc.close for ohlvc in data]
        volumes = [ohlvc.volume for ohlvc in data]

        # Timestamps zu datetime-Objekten konvertieren
        timestamps = []
        for ohlvc in data:
            # Binance Timestamps sind in Millisekunden, datetime.fromtimestamp erwartet Sekunden
            dt = datetime.fromtimestamp(ohlvc.timestamp / 1000)
            timestamps.append(dt)

        # Hauptdiagramm: Preisverlauf
        ax.plot(timestamps, prices, linewidth=2, color="#1c77b8", label=f'{symbol} Preis')

        # Durchschnittspreis berechnen und als horizontale Linie anzeigen
        avg_price = sum(prices) / len(prices)
        ax.axhline(y=avg_price, color='orange', linestyle='--', linewidth=2, alpha=0.8, label=f'Durchschnitt: {avg_price:.2f}')

        # Min- und Max-Preise markieren
        min_price = min(prices)
        max_price = max(prices)
        min_index = prices.index(min_price)
        max_index = prices.index(max_price)

        # Min-Preis markieren (roter Punkt)
        ax.plot(timestamps[min_index], min_price, 'ro', markersize=8, label=f'Min: {min_price:.2f}')
        ax.annotate(f'Min: {min_price:.2f}',
                    xy=(timestamps[min_index], min_price),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        # Max-Preis markieren (grüner Punkt)
        ax.plot(timestamps[max_index], max_price, 'go', markersize=8, label=f'Max: {max_price:.2f}')
        ax.annotate(f'Max: {max_price:.2f}',
                    xy=(timestamps[max_index], max_price),
                    xytext=(10, -20), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        ax.set_title(f'{symbol} Preisverlauf ({interval})', fontsize=16, fontweight='bold')
        ax.set_xlabel('Zeit', fontsize=12)
        ax.set_ylabel('Preis (USDT)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()

        # Datumsanzeige je nach Anzahl Datenpunkte formatieren
        data_count = len(timestamps)

        if data_count > 100:  # Viele Datenpunkte
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=12))
            ax.xaxis.set_minor_formatter(mdates.DateFormatter('%H:%M'))
            ax.xaxis.set_minor_locator(mdates.HourLocator(interval=12))
        elif data_count > 50:  # Mittlere Anzahl
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=12))
        else:  # Wenige Datenpunkte
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))

        # X-Achsen-Labels optimieren
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=9)

        # Mehr Platz für X-Achsen-Labels
        fig.subplots_adjust(bottom=0.15)

        # Sekundäre Y-Achse für Volumen
        ax2 = ax.twinx()
        ax2.bar(timestamps, volumes, alpha=0.3, color='orange', label='Volumen', width=0.02)
        ax2.set_ylabel('Volumen', fontsize=12, color='orange')
        ax2.tick_params(axis='y', labelcolor='orange')

        fig.tight_layout()
        return fig

    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Erstellt ein erweitertes Chart-Fenster mit Navigation und Statistiken
    # Zeigt Liniendiagramm mit detaillierten Informationen und Export-Optionen
    #----------------------------------------------------------------------------------------------
    def create_advanced_chart_window(self, data, symbol, interval):

        # Daten speichern
        self.current_data = data
        self.current_symbol = symbol
        self.current_interval = interval

        # Hauptfenster erstellen
        window = tk.Toplevel()
        window.geometry("1200x800")
        window.title(f"Erweiterte Charts - {symbol} ({interval})")

        # Hauptframe für das Liniendiagramm (ohne Tabs)
        main_frame = tk.Frame(window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Liniendiagramm erstellen
        line_fig = self.create_line_chart(main_frame, data, symbol, interval)
        line_canvas = FigureCanvasTkAgg(line_fig, master=main_frame)
        line_canvas.draw()
        line_canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        # Navigationsleiste hinzufügen
        line_toolbar = NavigationToolbar2Tk(line_canvas, main_frame)
        line_toolbar.update()

        # Statistik-Frame am unteren Rand
        self._create_stats_frame(window, data, symbol, interval)

        return window

    #-----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Erstellt einen Statistik-Frame mit detaillierten Marktdaten
    # Berechnet und zeigt Preis und Volumen
    #-----------------------------------------------------------------------------------------------
    def _create_stats_frame(self, parent, data, symbol, interval):

        stats_frame = tk.Frame(parent, bg='lightgray', height=120)
        stats_frame.pack(side=tk.BOTTOM, fill=tk.X)
        stats_frame.pack_propagate(False)

        if data:
            # Statistiken berechnen
            current_price = data[-1].close
            min_price = min(ohlvc.close for ohlvc in data)
            max_price = max(ohlvc.close for ohlvc in data)
            avg_price = sum(ohlvc.close for ohlvc in data) / len(data)
            total_volume = sum(ohlvc.volume for ohlvc in data)
            price_change = ((current_price - data[0].close) / data[0].close) * 100

            # Finde die tatsächlichen Datumswerte für Min- und Max-Preise
            min_price_ohlvc = min(data, key=lambda x: x.close)
            max_price_ohlvc = max(data, key=lambda x: x.close)
            min_price_datum = datetime.fromtimestamp(min_price_ohlvc.timestamp / 1000).strftime('%m-%d %H:%M')
            max_price_datum = datetime.fromtimestamp(max_price_ohlvc.timestamp / 1000).strftime('%m-%d %H:%M')

            # Volatilität berechnen
            price_changes = []
            for i in range(1, len(data)):
                change = ((data[i].close - data[i-1].close) / data[i-1].close) * 100
                price_changes.append(change)
            volatility = np.std(price_changes) if price_changes else 0 # Standardabweichung der Preisänderungen

            # Labels erstellen
            title_label = tk.Label(stats_frame, text=f"Statistiken für {symbol} ({interval})",
                                 bg='lightgray', font=('Arial', 12, 'bold'))
            title_label.pack(pady=2)

            # Statistik Anzeigelabels
            stats_text = f"Aktuell: {current_price:.2f} | Min: {min_price:.2f} am {min_price_datum} | Max: {max_price:.2f} am {max_price_datum} | Ø: {avg_price:.2f} | Änderung: {price_change:+.2f}%"
            stats_label = tk.Label(stats_frame, text=stats_text, bg='lightgray', font=('Arial', 10))
            stats_label.pack(pady=1)

            volume_text = f"Gesamtvolumen: {total_volume:.2f} | Datenpunkte: {len(data)} | Volatilität: {volatility:.2f}%"
            volume_label = tk.Label(stats_frame, text=volume_text, bg='lightgray', font=('Arial', 9))
            volume_label.pack(pady=1)

        # Button-Frame
        button_frame = tk.Frame(stats_frame, bg='lightgray')
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=5)

        # Schließen-Button
        close_btn = tk.Button(button_frame, text="Fenster schließen",
                             command=parent.destroy,
                             bg='red', fg='white', font=('Arial', 10, 'bold'))
        close_btn.pack(side=tk.RIGHT, padx=10)

        # Export-Button (Platzhalter)
        export_btn = tk.Button(button_frame, text="Chart exportieren",
                              command=lambda: print("Export-Funktion noch nicht implementiert"),
                              bg='blue', fg='white', font=('Arial', 10, 'bold'))
        export_btn.pack(side=tk.RIGHT, padx=5)