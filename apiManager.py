import requests
import json
from datetime import datetime
from DataObjects import OHLVC

class APIManager:  # <PERSON><PERSON><PERSON> sicher, dass die Klasse genau so heißt

    BASE_URL = "https://api.binance.com/api/v3/klines"

    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Initialisiert den APIManager mit leeren Standardwerten
    # Setzt Fehler-Status und Daten-Liste zurück
    #----------------------------------------------------------------------------------------------
    def __init__(self):
        self.last_error = None
        self.data = []  # Liste für die geladenen OHLVC-Daten

    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Validiert ein Kryptowährungs-Symbol für die Binance API
    # Prüft Format, Länge und erlaubte Zeichen des Symbols
    #----------------------------------------------------------------------------------------------
    def validate_symbol(self, symbol):
        # Binance-Symbole bestehen typischerweise aus Buchstaben und können Zahlen enthalten
        if not symbol or not isinstance(symbol, str):
            self.last_error = "Symbol muss ein gültiger String sein"
            return False
            
        # Entfernen der Leerzeichen und prüfe, ob das Symbol nicht leer ist
        symbol = symbol.strip()
        if not symbol:
            self.last_error = "Symbol darf nicht leer sein"
            return False
            
        # Prüfen, ob das Symbol nur alphanumerische Zeichen enthält
        if not symbol.isalnum():
            self.last_error = "Symbol darf nur Buchstaben und Zahlen enthalten"
            return False
            
        return True

    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Konvertiert ein Datum im Format 'YYYY-MM-DD' zu Unix-Timestamp
    # Wandelt Datum in Millisekunden-Timestamp für Binance API um
    #----------------------------------------------------------------------------------------------
    def date_to_timestamp(self, date_str):
       
        try:
            # Datums Format YYYY-MM-DD
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            # Umrechnung zu Unix-Timestamp in Millisekunden
            timestamp = int(date_obj.timestamp() * 1000)
            return timestamp
        except ValueError as e:
            self.last_error = f"Ungültiges Datumsformat. Verwenden Sie YYYY-MM-DD: {e}"
            return None

    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Lädt Krypto Daten von der Binance API
    # Fragt Parameter bei der API an und speichert in der Liste "data"
    # Speichert die Daten in der Klasse selbst
    #----------------------------------------------------------------------------------------------
    def fetch_data(self, symbol, interval, start_date, end_date):
     
        # Validierung des Symbols
        if not self.validate_symbol(symbol):
            print(f"Fehler: {self.last_error}")
            return None

        # Konvertierung der Daten zu Timestamps
        start_timestamp = self.date_to_timestamp(start_date)
        end_timestamp = self.date_to_timestamp(end_date)

        if start_timestamp is None or end_timestamp is None:
            print(f"Fehler: {self.last_error}")
            return None

        # Parameter für die API-Anfrage
        params = {
            'symbol': symbol.upper(),
            'interval': interval,
            'startTime': start_timestamp,
            'endTime': end_timestamp,
            'limit': 1000  # Maximale Anzahl von Datenpunkten
        }

        try: # Versucht die Daten zu laden
            print(f"Lade Daten für {symbol} von {start_date} bis {end_date}...")

            # HTTP-Anfrage an die Binance API
            response = requests.get(self.BASE_URL, params=params) # Erstellt ein HTTP request an die URL
            response.raise_for_status()  # Wirft eine Exception bei HTTP-Fehlern

            
            data = response.json()   # JSON ist ein Datenformat mit Daten

            # Konvertierung zu OHLVC-Objekten
            self.data = []
            for item in data: # item ist eine Liste mit den Datenpunkten
                # Binance API gibt folgende Daten zurück:
                # [timestamp, open, high, low, close, volume, close_time, quote_asset_volume, number_of_trades, taker_buy_base_asset_volume, taker_buy_quote_asset_volume, ignore]
                ohlvc = OHLVC(
                    float(item[1]),      # open
                    float(item[2]),      # high
                    float(item[3]),      # low
                    float(item[4]),      # close
                    float(item[5]),      # volume
                    int(item[0])         # timestamp in Millisekunden
                )
                self.data.append(ohlvc)

            print(f"Erfolgreich {len(self.data)} Datenpunkte geladen!")
            return self.data

        except requests.exceptions.RequestException as e:
            self.last_error = f"Netzwerkfehler: {e}"
            print(f"Fehler: {self.last_error}")
            return None
        except json.JSONDecodeError as e:
            self.last_error = f"Fehler beim Parsen der JSON-Daten: {e}"
            print(f"Fehler: {self.last_error}")
            return None
        except Exception as e:
            self.last_error = f"Unerwarteter Fehler: {e}"
            print(f"Fehler: {self.last_error}")
            return None

    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Returned data Liste mit den geladenen Daten
    #----------------------------------------------------------------------------------------------
    def get_data(self):
        return self.data

    #----------------------------------------------------------------------------------------------
    # Funktionsbeschreibung
    # Returned die letzte Fehlermeldung
    #----------------------------------------------------------------------------------------------
    def get_last_error(self):
        return self.last_error

