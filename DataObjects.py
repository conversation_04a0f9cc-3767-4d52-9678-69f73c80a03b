class OHLVC:
    def __init__(self, open, high, low, close, volume, timestamp):
        self.open = open
        self.high = high
        self.low = low
        self.close = close
        self.volume = volume
        self.timestamp = timestamp
        
    def set_volume(self, volume):
        self.volume = volume
    
    def get_volume(self):
        return self.volume

    def set_close(self, close):
        self.close = close
        
    def get_close(self):
        return self.close

    def set_low(self, low):
        self.low = low
        
    def get_low(self):
        return self.low

    def set_high(self, high):
        self.high = high
        
    def get_high(self):
        return self.high

    def set_open(self, open):
        self.open = open
        
    def get_open(self):
        return self.open
 
